const https = require('https');
const http = require('http');
const fs = require('fs');
const path = require('path');
const crypto = require('crypto');
const obsConfig = require('../config/obsConfig');

/**
 * OBS (Object Storage Service) Service
 * Provides comprehensive image storage operations for OBS-compatible storage
 */
class OBSService {
  constructor() {
    this.config = obsConfig;
  }

  /**
   * Make HTTP request to OBS
   * @param {string} method - HTTP method
   * @param {string} objectKey - Object key (optional)
   * @param {Object} options - Request options
   * @returns {Promise<Object>} Response data
   */
  async makeRequest(method, objectKey = '', options = {}) {
    return new Promise((resolve, reject) => {
      const url = new URL(objectKey ? this.config.getObjectUrl(objectKey) : this.config.getBucketUrl());
      const isHttps = url.protocol === 'https:';
      const httpModule = isHttps ? https : http;
      
      const headers = {
        'Host': url.host,
        'Date': new Date().toUTCString(),
        'Content-Type': options.contentType || 'application/octet-stream',
        ...options.headers
      };

      if (options.body) {
        headers['Content-Length'] = Buffer.byteLength(options.body);
        headers['Content-MD5'] = crypto.createHash('md5').update(options.body).digest('base64');
      }

      // Generate authorization header
      const authorization = this.config.generateSignature(method, url.pathname, headers, options.body || '');
      headers['Authorization'] = authorization;

      const requestOptions = {
        hostname: url.hostname,
        port: url.port || (isHttps ? 443 : 80),
        path: url.pathname + url.search,
        method: method,
        headers: headers
      };

      const req = httpModule.request(requestOptions, (res) => {
        let data = '';
        
        res.on('data', (chunk) => {
          data += chunk;
        });

        res.on('end', () => {
          const response = {
            statusCode: res.statusCode,
            headers: res.headers,
            body: data
          };

          if (res.statusCode >= 200 && res.statusCode < 300) {
            resolve(response);
          } else {
            reject(new Error(`OBS request failed: ${res.statusCode} - ${data}`));
          }
        });
      });

      req.on('error', (error) => {
        reject(error);
      });

      if (options.body) {
        req.write(options.body);
      }

      req.end();
    });
  }

  /**
   * Create bucket if it doesn't exist
   * @returns {Promise<boolean>} Success status
   */
  async createBucket() {
    try {
      // First check if bucket exists
      await this.makeRequest('HEAD');
      console.log('✅ Bucket already exists');
      return true;
    } catch (error) {
      if (error.message.includes('404')) {
        try {
          // Create bucket
          await this.makeRequest('PUT', '', {
            headers: {
              'x-obs-acl': 'private'
            }
          });
          console.log('✅ Bucket created successfully');
          return true;
        } catch (createError) {
          console.error('❌ Failed to create bucket:', createError.message);
          throw createError;
        }
      } else {
        console.error('❌ Error checking bucket:', error.message);
        throw error;
      }
    }
  }

  /**
   * Upload image to OBS
   * @param {Buffer|string} imageData - Image data (Buffer or base64 string)
   * @param {string} fileName - File name
   * @param {Object} metadata - Additional metadata
   * @returns {Promise<Object>} Upload result
   */
  async uploadImage(imageData, fileName, metadata = {}) {
    try {
      // Ensure bucket exists
      await this.createBucket();

      // Process image data
      let buffer;
      let contentType = 'image/jpeg';

      if (typeof imageData === 'string') {
        // Handle base64 data
        if (imageData.startsWith('data:')) {
          const matches = imageData.match(/^data:([^;]+);base64,(.+)$/);
          if (matches) {
            contentType = matches[1];
            buffer = Buffer.from(matches[2], 'base64');
          } else {
            throw new Error('Invalid base64 data format');
          }
        } else {
          // Assume it's a file path
          buffer = fs.readFileSync(imageData);
          contentType = this.getContentType(imageData);
        }
      } else if (Buffer.isBuffer(imageData)) {
        buffer = imageData;
      } else {
        throw new Error('Invalid image data format');
      }

      // Generate unique object key
      const timestamp = Date.now();
      const randomString = crypto.randomBytes(8).toString('hex');
      const extension = this.getFileExtension(fileName) || this.getExtensionFromContentType(contentType);
      const objectKey = `images/${timestamp}-${randomString}${extension}`;

      // Prepare headers
      const headers = {
        'Content-Type': contentType,
        'x-obs-meta-original-name': fileName,
        'x-obs-meta-upload-time': new Date().toISOString(),
        ...metadata
      };

      // Upload to OBS
      const response = await this.makeRequest('PUT', objectKey, {
        body: buffer,
        contentType: contentType,
        headers: headers
      });

      const result = {
        success: true,
        objectKey: objectKey,
        url: this.config.getObjectUrl(objectKey),
        size: buffer.length,
        contentType: contentType,
        fileName: fileName,
        uploadTime: new Date().toISOString(),
        etag: response.headers.etag
      };

      console.log('✅ Image uploaded successfully:', objectKey);
      return result;

    } catch (error) {
      console.error('❌ Image upload failed:', error.message);
      throw new Error(`Failed to upload image: ${error.message}`);
    }
  }

  /**
   * Download image from OBS
   * @param {string} objectKey - Object key
   * @returns {Promise<Buffer>} Image data
   */
  async downloadImage(objectKey) {
    try {
      const response = await this.makeRequest('GET', objectKey);
      return Buffer.from(response.body, 'binary');
    } catch (error) {
      console.error('❌ Image download failed:', error.message);
      throw new Error(`Failed to download image: ${error.message}`);
    }
  }

  /**
   * Delete image from OBS
   * @param {string} objectKey - Object key
   * @returns {Promise<boolean>} Success status
   */
  async deleteImage(objectKey) {
    try {
      await this.makeRequest('DELETE', objectKey);
      console.log('✅ Image deleted successfully:', objectKey);
      return true;
    } catch (error) {
      console.error('❌ Image deletion failed:', error.message);
      throw new Error(`Failed to delete image: ${error.message}`);
    }
  }

  /**
   * Get image metadata
   * @param {string} objectKey - Object key
   * @returns {Promise<Object>} Image metadata
   */
  async getImageMetadata(objectKey) {
    try {
      const response = await this.makeRequest('HEAD', objectKey);
      
      return {
        objectKey: objectKey,
        size: parseInt(response.headers['content-length']) || 0,
        contentType: response.headers['content-type'],
        lastModified: response.headers['last-modified'],
        etag: response.headers.etag,
        metadata: this.extractCustomMetadata(response.headers)
      };
    } catch (error) {
      console.error('❌ Failed to get image metadata:', error.message);
      throw new Error(`Failed to get image metadata: ${error.message}`);
    }
  }

  /**
   * List images in bucket
   * @param {Object} options - List options
   * @returns {Promise<Array>} List of images
   */
  async listImages(options = {}) {
    try {
      const queryParams = new URLSearchParams({
        prefix: options.prefix || 'images/',
        'max-keys': options.maxKeys || 1000
      });

      if (options.marker) {
        queryParams.set('marker', options.marker);
      }

      const url = `${this.config.getBucketUrl()}?${queryParams.toString()}`;
      const response = await this.makeRequest('GET', '', { url });
      
      // Parse XML response (simplified - you might want to use a proper XML parser)
      const images = this.parseListResponse(response.body);
      return images;
    } catch (error) {
      console.error('❌ Failed to list images:', error.message);
      throw new Error(`Failed to list images: ${error.message}`);
    }
  }

  /**
   * Generate presigned URL for temporary access
   * @param {string} objectKey - Object key
   * @param {number} expiresIn - Expiration time in seconds
   * @param {string} method - HTTP method
   * @returns {string} Presigned URL
   */
  generatePresignedUrl(objectKey, expiresIn = 3600, method = 'GET') {
    return this.config.generatePresignedUrl(objectKey, expiresIn, method);
  }

  // Helper methods
  getContentType(filePath) {
    const ext = path.extname(filePath).toLowerCase();
    const mimeTypes = {
      '.jpg': 'image/jpeg',
      '.jpeg': 'image/jpeg',
      '.png': 'image/png',
      '.gif': 'image/gif',
      '.webp': 'image/webp',
      '.svg': 'image/svg+xml'
    };
    return mimeTypes[ext] || 'application/octet-stream';
  }

  getFileExtension(fileName) {
    return path.extname(fileName);
  }

  getExtensionFromContentType(contentType) {
    const extensions = {
      'image/jpeg': '.jpg',
      'image/png': '.png',
      'image/gif': '.gif',
      'image/webp': '.webp',
      'image/svg+xml': '.svg'
    };
    return extensions[contentType] || '.jpg';
  }

  extractCustomMetadata(headers) {
    const metadata = {};
    Object.keys(headers).forEach(key => {
      if (key.startsWith('x-obs-meta-')) {
        const metaKey = key.replace('x-obs-meta-', '');
        metadata[metaKey] = headers[key];
      }
    });
    return metadata;
  }

  parseListResponse(xmlBody) {
    // Simplified XML parsing - in production, use a proper XML parser
    const images = [];
    const keyRegex = /<Key>(.*?)<\/Key>/g;
    const sizeRegex = /<Size>(.*?)<\/Size>/g;
    const lastModifiedRegex = /<LastModified>(.*?)<\/LastModified>/g;
    
    let keyMatch, sizeMatch, lastModifiedMatch;
    let index = 0;
    
    while ((keyMatch = keyRegex.exec(xmlBody)) !== null) {
      sizeMatch = sizeRegex.exec(xmlBody);
      lastModifiedMatch = lastModifiedRegex.exec(xmlBody);
      
      images.push({
        key: keyMatch[1],
        size: sizeMatch ? parseInt(sizeMatch[1]) : 0,
        lastModified: lastModifiedMatch ? lastModifiedMatch[1] : null,
        url: this.config.getObjectUrl(keyMatch[1])
      });
      index++;
    }
    
    return images;
  }
}

module.exports = new OBSService();
