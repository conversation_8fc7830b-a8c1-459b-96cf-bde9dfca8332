# OBS (Object Storage Service) Integration Guide

## Overview

This document provides comprehensive guidance for integrating OBS (Object Storage Service) with the OnPrintz application. OBS is a cloud storage solution that provides scalable, secure, and cost-effective object storage for images and other files.

## Table of Contents

1. [Prerequisites](#prerequisites)
2. [Configuration](#configuration)
3. [Backend Integration](#backend-integration)
4. [Frontend Integration](#frontend-integration)
5. [Testing](#testing)
6. [Migration from Cloudinary](#migration-from-cloudinary)
7. [API Reference](#api-reference)
8. [Troubleshooting](#troubleshooting)

## Prerequisites

### Required Environment Variables

Create a `.env` file in your server directory with the following variables:

```env
# OBS Configuration
OBS_ACCESS_KEY_ID=your_access_key_id
OBS_SECRET_ACCESS_KEY=your_secret_access_key
OBS_ENDPOINT=obs.your-region.example.com
OBS_BUCKET_NAME=onprintz-images
OBS_REGION=your-region

# Optional: Frontend Configuration
REACT_APP_OBS_ENDPOINT=obs.your-region.example.com
```

### Dependencies

The OBS integration uses existing dependencies. No additional packages are required.

## Configuration

### 1. OBS Service Setup

The OBS configuration is automatically loaded from environment variables:

```javascript
// server/config/obsConfig.js
const obsConfig = require('./config/obsConfig');

// Access configuration
console.log(obsConfig.bucketName); // 'onprintz-images'
console.log(obsConfig.getBaseUrl()); // 'https://onprintz-images.obs.your-region.example.com'
```

### 2. Bucket Creation

The OBS service automatically creates the bucket if it doesn't exist:

```javascript
const obsService = require('./services/obsService');

// Bucket is created automatically during first upload
await obsService.uploadImage(imageData, 'filename.jpg');
```

## Backend Integration

### 1. Using OBS Image Controller

The OBS image controller provides the same functionality as the original image controller:

```javascript
// server/routes/image/obsImageRoutes.js
const router = require('express').Router();
const obsImageCtrl = require('../../controllers/image/obsImageCtrl');

// All standard image operations
router.post('/upload', obsImageCtrl.uploadOBSImage);
router.get('/', obsImageCtrl.getAllOBSImages);
router.get('/active', obsImageCtrl.getAllActiveOBSImages);
router.put('/:id', obsImageCtrl.updateOBSImage);
router.delete('/:id', obsImageCtrl.deleteOBSImage);
```

### 2. Direct OBS Service Usage

For custom implementations, use the OBS service directly:

```javascript
const obsService = require('./services/obsService');

// Upload image
const result = await obsService.uploadImage(
  imageBuffer, // Buffer, file path, or base64 string
  'original-filename.jpg',
  {
    'x-obs-meta-uploader': userId,
    'x-obs-meta-category': 'product-images'
  }
);

// Download image
const imageBuffer = await obsService.downloadImage(result.objectKey);

// Delete image
await obsService.deleteImage(result.objectKey);

// Get metadata
const metadata = await obsService.getImageMetadata(result.objectKey);
```

### 3. Adding OBS Routes to Express App

Add the OBS routes to your main Express application:

```javascript
// server/index.js
const obsImageRoutes = require('./routes/image/obsImageRoutes');

app.use('/api/obs-images', obsImageRoutes);
```

## Frontend Integration

### 1. Redux Store Setup

#### OBS Image Service (`client/src/store/obsImage/obsImageService.js`)

```javascript
import { axiosPrivate } from "../../api/axios";

/**
 * Get all OBS images
 * @returns {Promise<Object>} Response data
 */
const getAllOBSImages = async () => {
  const response = await axiosPrivate.get(`/obs-images`);
  return response.data;
};

/**
 * Get all active OBS images
 * @returns {Promise<Object>} Response data
 */
const getAllActiveOBSImages = async () => {
  const response = await axiosPrivate.get(`/obs-images/active`);
  return response.data;
};

/**
 * Get single OBS image by object key
 * @param {string} objectKey - Object key
 * @returns {Promise<Object>} Response data
 */
const getOBSImageById = async (objectKey) => {
  const response = await axiosPrivate.get(`/obs-images/${encodeURIComponent(objectKey)}`);
  return response.data;
};

/**
 * Upload images to OBS
 * @param {Object} uploadData - Upload data with files and metadata
 * @returns {Promise<Object>} Response data
 */
const uploadImagesToOBS = async (uploadData) => {
  const response = await axiosPrivate.post(`/obs-images/upload`, uploadData, {
    headers: {
      "Content-Type": "multipart/form-data",
    },
  });
  return response.data;
};

/**
 * Update OBS image
 * @param {string} id - Image ID
 * @param {Object} updateData - Update data
 * @returns {Promise<Object>} Response data
 */
const updateOBSImage = async (id, updateData) => {
  const response = await axiosPrivate.put(`/obs-images/${id}`, updateData);
  return response.data;
};

/**
 * Delete OBS image
 * @param {string} objectKey - Object key
 * @returns {Promise<Object>} Response data
 */
const deleteOBSImage = async (objectKey) => {
  const response = await axiosPrivate.delete(`/obs-images/${encodeURIComponent(objectKey)}`);
  return response.data;
};

/**
 * Update OBS image status
 * @param {string} id - Image ID
 * @param {string} status - New status
 * @returns {Promise<Object>} Response data
 */
const updateOBSImageStatus = async (id, status) => {
  const response = await axiosPrivate.patch(`/obs-images/${id}/status`, { status });
  return response.data;
};

/**
 * Bulk delete OBS images
 * @param {Array} objectKeys - Array of object keys
 * @returns {Promise<Object>} Response data
 */
const bulkDeleteOBSImages = async (objectKeys) => {
  const response = await axiosPrivate.delete(`/obs-images/bulk`, {
    data: { objectKeys }
  });
  return response.data;
};

const obsImageService = {
  getAllOBSImages,
  getAllActiveOBSImages,
  getOBSImageById,
  uploadImagesToOBS,
  updateOBSImage,
  deleteOBSImage,
  updateOBSImageStatus,
  bulkDeleteOBSImages,
};

export default obsImageService;
```

#### OBS Image Slice (`client/src/store/obsImage/obsImageSlice.js`)

```javascript
import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import obsImageService from "./obsImageService";
import toast from "react-hot-toast";

const initialState = {
  images: [],
  activeImages: [],
  selectedImage: null,
  uploadProgress: 0,
  isLoading: false,
  isUploading: false,
  isDeleting: false,
  isSuccess: false,
  isError: false,
  message: "",
  pagination: {
    currentPage: 1,
    totalPages: 1,
    totalImages: 0,
    hasNextPage: false,
    hasPrevPage: false,
  },
};

/**
 * Get all OBS images
 */
export const getAllOBSImages = createAsyncThunk(
  "obsImage/getAllImages",
  async (_, thunkAPI) => {
    try {
      return await obsImageService.getAllOBSImages();
    } catch (error) {
      return thunkAPI.rejectWithValue(error);
    }
  }
);

/**
 * Get all active OBS images
 */
export const getAllActiveOBSImages = createAsyncThunk(
  "obsImage/getAllActiveImages",
  async (_, thunkAPI) => {
    try {
      return await obsImageService.getAllActiveOBSImages();
    } catch (error) {
      return thunkAPI.rejectWithValue(error);
    }
  }
);

/**
 * Get OBS image by object key
 */
export const getOBSImageById = createAsyncThunk(
  "obsImage/getImageById",
  async (objectKey, thunkAPI) => {
    try {
      return await obsImageService.getOBSImageById(objectKey);
    } catch (error) {
      return thunkAPI.rejectWithValue(error);
    }
  }
);

/**
 * Upload images to OBS
 */
export const uploadImagesToOBS = createAsyncThunk(
  "obsImage/uploadImages",
  async (uploadData, thunkAPI) => {
    try {
      return await obsImageService.uploadImagesToOBS(uploadData);
    } catch (error) {
      return thunkAPI.rejectWithValue(error);
    }
  }
);

/**
 * Update OBS image
 */
export const updateOBSImage = createAsyncThunk(
  "obsImage/updateImage",
  async ({ id, updateData }, thunkAPI) => {
    try {
      return await obsImageService.updateOBSImage(id, updateData);
    } catch (error) {
      return thunkAPI.rejectWithValue(error);
    }
  }
);

/**
 * Delete OBS image
 */
export const deleteOBSImage = createAsyncThunk(
  "obsImage/deleteImage",
  async (objectKey, thunkAPI) => {
    try {
      return await obsImageService.deleteOBSImage(objectKey);
    } catch (error) {
      return thunkAPI.rejectWithValue(error);
    }
  }
);

/**
 * Update OBS image status
 */
export const updateOBSImageStatus = createAsyncThunk(
  "obsImage/updateImageStatus",
  async ({ id, status }, thunkAPI) => {
    try {
      return await obsImageService.updateOBSImageStatus(id, status);
    } catch (error) {
      return thunkAPI.rejectWithValue(error);
    }
  }
);

/**
 * Bulk delete OBS images
 */
export const bulkDeleteOBSImages = createAsyncThunk(
  "obsImage/bulkDeleteImages",
  async (objectKeys, thunkAPI) => {
    try {
      return await obsImageService.bulkDeleteOBSImages(objectKeys);
    } catch (error) {
      return thunkAPI.rejectWithValue(error);
    }
  }
);

const obsImageSlice = createSlice({
  name: "obsImage",
  initialState,
  reducers: {
    reset: (state) => {
      state.isLoading = false;
      state.isUploading = false;
      state.isDeleting = false;
      state.isSuccess = false;
      state.isError = false;
      state.message = "";
      state.uploadProgress = 0;
    },
    clearSelectedImage: (state) => {
      state.selectedImage = null;
    },
    setUploadProgress: (state, action) => {
      state.uploadProgress = action.payload;
    },
  },
  extraReducers: (builder) => {
    builder
      // Get all images
      .addCase(getAllOBSImages.pending, (state) => {
        state.isLoading = true;
        state.isError = false;
      })
      .addCase(getAllOBSImages.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.images = action.payload.data || [];
        state.pagination = action.payload.pagination || state.pagination;
      })
      .addCase(getAllOBSImages.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.message = action.payload?.message || "Failed to fetch images";
        toast.error(state.message);
      })
      // Get active images
      .addCase(getAllActiveOBSImages.pending, (state) => {
        state.isLoading = true;
        state.isError = false;
      })
      .addCase(getAllActiveOBSImages.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.activeImages = action.payload.data || [];
      })
      .addCase(getAllActiveOBSImages.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.message = action.payload?.message || "Failed to fetch active images";
        toast.error(state.message);
      })
      // Get image by ID
      .addCase(getOBSImageById.pending, (state) => {
        state.isLoading = true;
        state.isError = false;
      })
      .addCase(getOBSImageById.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.selectedImage = action.payload.data;
      })
      .addCase(getOBSImageById.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.message = action.payload?.message || "Failed to fetch image";
        toast.error(state.message);
      })
      // Upload images
      .addCase(uploadImagesToOBS.pending, (state) => {
        state.isUploading = true;
        state.isError = false;
        state.uploadProgress = 0;
      })
      .addCase(uploadImagesToOBS.fulfilled, (state, action) => {
        state.isUploading = false;
        state.isSuccess = true;
        state.uploadProgress = 100;
        // Add new images to the arrays
        if (action.payload.data) {
          state.images = [...state.images, ...action.payload.data];
          state.activeImages = [...state.activeImages, ...action.payload.data];
        }
        toast.success(action.payload.message || "Images uploaded successfully");
      })
      .addCase(uploadImagesToOBS.rejected, (state, action) => {
        state.isUploading = false;
        state.isError = true;
        state.uploadProgress = 0;
        state.message = action.payload?.message || "Upload failed";
        toast.error(state.message);
      })
      // Update image
      .addCase(updateOBSImage.pending, (state) => {
        state.isLoading = true;
        state.isError = false;
      })
      .addCase(updateOBSImage.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        // Update image in arrays
        const updatedImage = action.payload.data;
        state.images = state.images.map(img =>
          img._id === updatedImage._id ? updatedImage : img
        );
        state.activeImages = state.activeImages.map(img =>
          img._id === updatedImage._id ? updatedImage : img
        );
        toast.success("Image updated successfully");
      })
      .addCase(updateOBSImage.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.message = action.payload?.message || "Update failed";
        toast.error(state.message);
      })
      // Delete image
      .addCase(deleteOBSImage.pending, (state) => {
        state.isDeleting = true;
        state.isError = false;
      })
      .addCase(deleteOBSImage.fulfilled, (state, action) => {
        state.isDeleting = false;
        state.isSuccess = true;
        // Remove image from arrays
        const deletedObjectKey = action.meta.arg;
        state.images = state.images.filter(img => img.objectKey !== deletedObjectKey);
        state.activeImages = state.activeImages.filter(img => img.objectKey !== deletedObjectKey);
        toast.success("Image deleted successfully");
      })
      .addCase(deleteOBSImage.rejected, (state, action) => {
        state.isDeleting = false;
        state.isError = true;
        state.message = action.payload?.message || "Delete failed";
        toast.error(state.message);
      })
      // Update image status
      .addCase(updateOBSImageStatus.fulfilled, (state, action) => {
        const updatedImage = action.payload.data;
        state.images = state.images.map(img =>
          img._id === updatedImage._id ? updatedImage : img
        );
        // Update active images based on status
        if (updatedImage.status === 'active') {
          state.activeImages = state.activeImages.map(img =>
            img._id === updatedImage._id ? updatedImage : img
          );
        } else {
          state.activeImages = state.activeImages.filter(img => img._id !== updatedImage._id);
        }
        toast.success("Image status updated");
      })
      // Bulk delete
      .addCase(bulkDeleteOBSImages.fulfilled, (state, action) => {
        const deletedObjectKeys = action.meta.arg;
        state.images = state.images.filter(img => !deletedObjectKeys.includes(img.objectKey));
        state.activeImages = state.activeImages.filter(img => !deletedObjectKeys.includes(img.objectKey));
        toast.success(`${deletedObjectKeys.length} images deleted successfully`);
      });
  },
});

export const { reset, clearSelectedImage, setUploadProgress } = obsImageSlice.actions;
export default obsImageSlice.reducer;
```

### 2. Store Configuration

Add the OBS image slice to your store configuration:

```javascript
// client/src/store/store.js
import { configureStore } from "@reduxjs/toolkit";
import authReducer from "./auth/authSlice";
import productReducer from "./product/productSlice";
import obsImageReducer from "./obsImage/obsImageSlice"; // Add this

export const store = configureStore({
  reducer: {
    auth: authReducer,
    product: productReducer,
    obsImage: obsImageReducer, // Add this
  },
});
```

### 3. Using Redux in React Components

```jsx
// Upload Component with Redux
import React, { useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { uploadImagesToOBS, reset } from '../store/obsImage/obsImageSlice';

function OBSUploadPage() {
  const dispatch = useDispatch();
  const { isUploading, uploadProgress, isSuccess, isError, message } = useSelector(
    (state) => state.obsImage
  );

  const [files, setFiles] = useState([]);
  const [categories, setCategories] = useState([]);
  const [types, setTypes] = useState([]);

  const handleUpload = async () => {
    const formData = new FormData();
    files.forEach(file => formData.append('images', file));
    categories.forEach(cat => formData.append('image_categories', cat));
    types.forEach(type => formData.append('image_types', type));

    dispatch(uploadImagesToOBS(formData));
  };

  React.useEffect(() => {
    if (isSuccess) {
      setFiles([]);
      setCategories([]);
      setTypes([]);
      dispatch(reset());
    }
  }, [isSuccess, dispatch]);

  return (
    <div>
      <input
        type="file"
        multiple
        onChange={(e) => setFiles(Array.from(e.target.files))}
        disabled={isUploading}
      />

      {isUploading && (
        <div>
          <div>Uploading... {uploadProgress}%</div>
          <progress value={uploadProgress} max="100" />
        </div>
      )}

      <button
        onClick={handleUpload}
        disabled={isUploading || files.length === 0}
      >
        {isUploading ? 'Uploading...' : 'Upload Images'}
      </button>
    </div>
  );
}

// Gallery Component with Redux
import React, { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { getAllActiveOBSImages, deleteOBSImage } from '../store/obsImage/obsImageSlice';

function OBSGalleryPage() {
  const dispatch = useDispatch();
  const { activeImages, isLoading, isDeleting } = useSelector(
    (state) => state.obsImage
  );

  useEffect(() => {
    dispatch(getAllActiveOBSImages());
  }, [dispatch]);

  const handleDelete = (objectKey) => {
    if (window.confirm('Are you sure you want to delete this image?')) {
      dispatch(deleteOBSImage(objectKey));
    }
  };

  if (isLoading) return <div>Loading images...</div>;

  return (
    <div className="grid grid-cols-3 gap-4">
      {activeImages.map((image) => (
        <div key={image._id} className="relative">
          <img
            src={image.image[0]}
            alt={image.objectKey}
            className="w-full h-48 object-cover rounded"
          />
          <button
            onClick={() => handleDelete(image.objectKey)}
            disabled={isDeleting}
            className="absolute top-2 right-2 bg-red-500 text-white p-1 rounded"
          >
            {isDeleting ? '...' : '×'}
          </button>
        </div>
      ))}
    </div>
  );
}
```

## Testing

### 1. Running Tests

```bash
# Run all OBS tests
npm test -- --testPathPattern=obs

# Run specific test files
npm test tests/obsService.test.js
npm test tests/obsImageCtrl.test.js
```

### 2. Test Configuration

Set up test environment variables:

```env
# .env.test
OBS_ACCESS_KEY_ID=test-access-key
OBS_SECRET_ACCESS_KEY=test-secret-key
OBS_ENDPOINT=obs.test-region.example.com
OBS_BUCKET_NAME=test-bucket
OBS_REGION=test-region
MONGODB_TEST_URI=mongodb://localhost:27017/onprintz-test
```

### 3. Manual Testing

Test the integration manually:

```bash
# Start the server
npm run server

# Test upload endpoint
curl -X POST http://localhost:5000/api/obs-images/upload \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -F "image=@test-image.jpg" \
  -F "image_categories=CATEGORY_ID" \
  -F "image_types=TYPE_ID"

# Test get images endpoint
curl http://localhost:5000/api/obs-images/active
```

## Migration from Cloudinary

### 1. Gradual Migration Strategy

1. **Phase 1**: Set up OBS integration alongside Cloudinary
2. **Phase 2**: Test OBS with new uploads
3. **Phase 3**: Migrate existing images (optional)
4. **Phase 4**: Switch default storage to OBS
5. **Phase 5**: Remove Cloudinary integration

### 2. Migration Script Example

```javascript
// scripts/migrate-to-obs.js
const Image = require('../server/models/image/imageModel');
const obsService = require('../server/services/obsService');
const { downloadImageFromCloudinary } = require('../server/utils/cloudinary');

async function migrateImagesToOBS() {
  const images = await Image.find({ 
    image: { $regex: 'cloudinary.com' } 
  });

  for (const image of images) {
    try {
      // Download from Cloudinary
      const imageBuffer = await downloadImageFromCloudinary(image.image[0]);
      
      // Upload to OBS
      const result = await obsService.uploadImage(
        imageBuffer,
        `migrated-${image._id}.jpg`,
        {
          'x-obs-meta-migrated-from': 'cloudinary',
          'x-obs-meta-original-id': image._id.toString()
        }
      );

      // Update database record
      await Image.findByIdAndUpdate(image._id, {
        image: [result.url],
        $push: { 
          migrationLog: {
            from: 'cloudinary',
            to: 'obs',
            migratedAt: new Date()
          }
        }
      });

      console.log(`✅ Migrated image ${image._id}`);
    } catch (error) {
      console.error(`❌ Failed to migrate image ${image._id}:`, error);
    }
  }
}
```

### 3. Dual Storage Support

Support both Cloudinary and OBS during migration:

```javascript
// utils/imageStorage.js
const cloudinary = require('./cloudinary');
const obsService = require('../services/obsService');

async function uploadImage(imageData, filename, storage = 'obs') {
  if (storage === 'obs') {
    return await obsService.uploadImage(imageData, filename);
  } else {
    return await cloudinary.uploadImage(imageData, filename);
  }
}

function isOBSUrl(url) {
  return url.includes(process.env.OBS_ENDPOINT);
}

async function deleteImage(imageUrl) {
  if (isOBSUrl(imageUrl)) {
    const objectKey = extractObjectKeyFromUrl(imageUrl);
    return await obsService.deleteImage(objectKey);
  } else {
    return await cloudinary.deleteImageByUrl(imageUrl);
  }
}
```

## API Reference

### OBS Image Endpoints

| Method | Endpoint | Description |
|--------|----------|-------------|
| POST | `/api/obs-images` | Create image record |
| GET | `/api/obs-images` | Get all images |
| GET | `/api/obs-images/active` | Get active images |
| POST | `/api/obs-images/upload` | Upload images to OBS |
| PUT | `/api/obs-images/:id` | Update image |
| DELETE | `/api/obs-images/:id` | Delete image |
| PATCH | `/api/obs-images/:id/status` | Update image status |
| DELETE | `/api/obs-images/bulk` | Bulk delete images |

### Request/Response Examples

#### Upload Image

**Request:**
```bash
POST /api/obs-images/upload
Content-Type: multipart/form-data

image: [file]
image_categories: ["category_id_1", "category_id_2"]
image_types: ["type_id_1"]
```

**Response:**
```json
{
  "success": true,
  "message": "Images uploaded successfully to OBS",
  "data": [
    {
      "_id": "image_id",
      "image": ["https://bucket.obs.example.com/images/123-abc.jpg"],
      "image_category": ["category_id_1"],
      "image_type": ["type_id_1"],
      "status": "pending",
      "createdAt": "2025-01-01T00:00:00.000Z"
    }
  ]
}
```

## Troubleshooting

### Common Issues

1. **Authentication Errors**
   - Verify OBS credentials in environment variables
   - Check access key permissions
   - Ensure bucket access is configured

2. **Upload Failures**
   - Check file size limits (10MB default)
   - Verify file type restrictions
   - Check network connectivity to OBS endpoint

3. **CORS Issues**
   - Configure CORS settings on OBS bucket
   - Add your domain to allowed origins

4. **Performance Issues**
   - Implement image compression before upload
   - Use CDN for image delivery
   - Consider thumbnail generation

### Debug Mode

Enable debug logging:

```javascript
// Set environment variable
DEBUG=obs:*

// Or in code
process.env.DEBUG = 'obs:*';
```

### Health Check

Test OBS connectivity:

```javascript
const obsService = require('./services/obsService');

async function healthCheck() {
  try {
    await obsService.createBucket();
    console.log('✅ OBS connection successful');
  } catch (error) {
    console.error('❌ OBS connection failed:', error);
  }
}
```

## Best Practices

1. **Security**
   - Use IAM roles instead of access keys when possible
   - Implement proper access controls
   - Enable encryption at rest

2. **Performance**
   - Implement client-side image compression
   - Use progressive JPEG format
   - Consider WebP format for modern browsers

3. **Monitoring**
   - Monitor upload success rates
   - Track storage usage
   - Set up alerts for failures

4. **Backup**
   - Implement cross-region replication
   - Regular backup verification
   - Document recovery procedures

## Support

For issues or questions:
1. Check the troubleshooting section
2. Review OBS service documentation
3. Contact the development team
