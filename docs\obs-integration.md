# OBS (Object Storage Service) Integration Guide

## Overview

This document provides comprehensive guidance for integrating OBS (Object Storage Service) with the OnPrintz application. OBS is a cloud storage solution that provides scalable, secure, and cost-effective object storage for images and other files.

## Table of Contents

1. [Prerequisites](#prerequisites)
2. [Configuration](#configuration)
3. [Backend Integration](#backend-integration)
4. [Frontend Integration](#frontend-integration)
5. [Testing](#testing)
6. [Migration from Cloudinary](#migration-from-cloudinary)
7. [API Reference](#api-reference)
8. [Troubleshooting](#troubleshooting)

## Prerequisites

### Required Environment Variables

Create a `.env` file in your server directory with the following variables:

```env
# OBS Configuration
OBS_ACCESS_KEY_ID=your_access_key_id
OBS_SECRET_ACCESS_KEY=your_secret_access_key
OBS_ENDPOINT=obs.your-region.example.com
OBS_BUCKET_NAME=onprintz-images
OBS_REGION=your-region

# Optional: Frontend Configuration
REACT_APP_OBS_ENDPOINT=obs.your-region.example.com
```

### Dependencies

The OBS integration uses existing dependencies. No additional packages are required.

## Configuration

### 1. OBS Service Setup

The OBS configuration is automatically loaded from environment variables:

```javascript
// server/config/obsConfig.js
const obsConfig = require('./config/obsConfig');

// Access configuration
console.log(obsConfig.bucketName); // 'onprintz-images'
console.log(obsConfig.getBaseUrl()); // 'https://onprintz-images.obs.your-region.example.com'
```

### 2. Bucket Creation

The OBS service automatically creates the bucket if it doesn't exist:

```javascript
const obsService = require('./services/obsService');

// Bucket is created automatically during first upload
await obsService.uploadImage(imageData, 'filename.jpg');
```

## Backend Integration

### 1. Using OBS Image Controller

The OBS image controller provides the same functionality as the original image controller:

```javascript
// server/routes/image/obsImageRoutes.js
const router = require('express').Router();
const obsImageCtrl = require('../../controllers/image/obsImageCtrl');

// All standard image operations
router.post('/upload', obsImageCtrl.uploadOBSImage);
router.get('/', obsImageCtrl.getAllOBSImages);
router.get('/active', obsImageCtrl.getAllActiveOBSImages);
router.put('/:id', obsImageCtrl.updateOBSImage);
router.delete('/:id', obsImageCtrl.deleteOBSImage);
```

### 2. Direct OBS Service Usage

For custom implementations, use the OBS service directly:

```javascript
const obsService = require('./services/obsService');

// Upload image
const result = await obsService.uploadImage(
  imageBuffer, // Buffer, file path, or base64 string
  'original-filename.jpg',
  {
    'x-obs-meta-uploader': userId,
    'x-obs-meta-category': 'product-images'
  }
);

// Download image
const imageBuffer = await obsService.downloadImage(result.objectKey);

// Delete image
await obsService.deleteImage(result.objectKey);

// Get metadata
const metadata = await obsService.getImageMetadata(result.objectKey);
```

### 3. Adding OBS Routes to Express App

Add the OBS routes to your main Express application:

```javascript
// server/index.js
const obsImageRoutes = require('./routes/image/obsImageRoutes');

app.use('/api/obs-images', obsImageRoutes);
```

## Frontend Integration

### 1. Using OBS Image Service

```javascript
// client/src/services/obsImageService.js
import { uploadImagesToOBS, getAllActiveOBSImages } from './services/obsImageService';

// Upload images
const result = await uploadImagesToOBS(
  files, // FileList or File array
  categoryIds, // Array of category IDs
  typeIds, // Array of type IDs
  (progress) => console.log(`Upload progress: ${progress}%`)
);

// Get images
const images = await getAllActiveOBSImages();
```

### 2. Using React Components

```jsx
// Upload Component
import OBSImageUpload from './components/OBSImageUpload';

function MyUploadPage() {
  const handleUploadSuccess = (result) => {
    console.log('Upload successful:', result);
  };

  return (
    <OBSImageUpload
      categories={categories}
      types={types}
      onUploadSuccess={handleUploadSuccess}
      multiple={true}
    />
  );
}

// Gallery Component
import OBSImageGallery from './components/OBSImageGallery';

function MyGalleryPage() {
  const handleImageSelect = (image) => {
    console.log('Selected image:', image);
  };

  return (
    <OBSImageGallery
      showActions={true}
      onImageSelect={handleImageSelect}
      userRole="administrator"
    />
  );
}
```

## Testing

### 1. Running Tests

```bash
# Run all OBS tests
npm test -- --testPathPattern=obs

# Run specific test files
npm test tests/obsService.test.js
npm test tests/obsImageCtrl.test.js
```

### 2. Test Configuration

Set up test environment variables:

```env
# .env.test
OBS_ACCESS_KEY_ID=test-access-key
OBS_SECRET_ACCESS_KEY=test-secret-key
OBS_ENDPOINT=obs.test-region.example.com
OBS_BUCKET_NAME=test-bucket
OBS_REGION=test-region
MONGODB_TEST_URI=mongodb://localhost:27017/onprintz-test
```

### 3. Manual Testing

Test the integration manually:

```bash
# Start the server
npm run server

# Test upload endpoint
curl -X POST http://localhost:5000/api/obs-images/upload \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -F "image=@test-image.jpg" \
  -F "image_categories=CATEGORY_ID" \
  -F "image_types=TYPE_ID"

# Test get images endpoint
curl http://localhost:5000/api/obs-images/active
```

## Migration from Cloudinary

### 1. Gradual Migration Strategy

1. **Phase 1**: Set up OBS integration alongside Cloudinary
2. **Phase 2**: Test OBS with new uploads
3. **Phase 3**: Migrate existing images (optional)
4. **Phase 4**: Switch default storage to OBS
5. **Phase 5**: Remove Cloudinary integration

### 2. Migration Script Example

```javascript
// scripts/migrate-to-obs.js
const Image = require('../server/models/image/imageModel');
const obsService = require('../server/services/obsService');
const { downloadImageFromCloudinary } = require('../server/utils/cloudinary');

async function migrateImagesToOBS() {
  const images = await Image.find({ 
    image: { $regex: 'cloudinary.com' } 
  });

  for (const image of images) {
    try {
      // Download from Cloudinary
      const imageBuffer = await downloadImageFromCloudinary(image.image[0]);
      
      // Upload to OBS
      const result = await obsService.uploadImage(
        imageBuffer,
        `migrated-${image._id}.jpg`,
        {
          'x-obs-meta-migrated-from': 'cloudinary',
          'x-obs-meta-original-id': image._id.toString()
        }
      );

      // Update database record
      await Image.findByIdAndUpdate(image._id, {
        image: [result.url],
        $push: { 
          migrationLog: {
            from: 'cloudinary',
            to: 'obs',
            migratedAt: new Date()
          }
        }
      });

      console.log(`✅ Migrated image ${image._id}`);
    } catch (error) {
      console.error(`❌ Failed to migrate image ${image._id}:`, error);
    }
  }
}
```

### 3. Dual Storage Support

Support both Cloudinary and OBS during migration:

```javascript
// utils/imageStorage.js
const cloudinary = require('./cloudinary');
const obsService = require('../services/obsService');

async function uploadImage(imageData, filename, storage = 'obs') {
  if (storage === 'obs') {
    return await obsService.uploadImage(imageData, filename);
  } else {
    return await cloudinary.uploadImage(imageData, filename);
  }
}

function isOBSUrl(url) {
  return url.includes(process.env.OBS_ENDPOINT);
}

async function deleteImage(imageUrl) {
  if (isOBSUrl(imageUrl)) {
    const objectKey = extractObjectKeyFromUrl(imageUrl);
    return await obsService.deleteImage(objectKey);
  } else {
    return await cloudinary.deleteImageByUrl(imageUrl);
  }
}
```

## API Reference

### OBS Image Endpoints

| Method | Endpoint | Description |
|--------|----------|-------------|
| POST | `/api/obs-images` | Create image record |
| GET | `/api/obs-images` | Get all images |
| GET | `/api/obs-images/active` | Get active images |
| POST | `/api/obs-images/upload` | Upload images to OBS |
| PUT | `/api/obs-images/:id` | Update image |
| DELETE | `/api/obs-images/:id` | Delete image |
| PATCH | `/api/obs-images/:id/status` | Update image status |
| DELETE | `/api/obs-images/bulk` | Bulk delete images |

### Request/Response Examples

#### Upload Image

**Request:**
```bash
POST /api/obs-images/upload
Content-Type: multipart/form-data

image: [file]
image_categories: ["category_id_1", "category_id_2"]
image_types: ["type_id_1"]
```

**Response:**
```json
{
  "success": true,
  "message": "Images uploaded successfully to OBS",
  "data": [
    {
      "_id": "image_id",
      "image": ["https://bucket.obs.example.com/images/123-abc.jpg"],
      "image_category": ["category_id_1"],
      "image_type": ["type_id_1"],
      "status": "pending",
      "createdAt": "2025-01-01T00:00:00.000Z"
    }
  ]
}
```

## Troubleshooting

### Common Issues

1. **Authentication Errors**
   - Verify OBS credentials in environment variables
   - Check access key permissions
   - Ensure bucket access is configured

2. **Upload Failures**
   - Check file size limits (10MB default)
   - Verify file type restrictions
   - Check network connectivity to OBS endpoint

3. **CORS Issues**
   - Configure CORS settings on OBS bucket
   - Add your domain to allowed origins

4. **Performance Issues**
   - Implement image compression before upload
   - Use CDN for image delivery
   - Consider thumbnail generation

### Debug Mode

Enable debug logging:

```javascript
// Set environment variable
DEBUG=obs:*

// Or in code
process.env.DEBUG = 'obs:*';
```

### Health Check

Test OBS connectivity:

```javascript
const obsService = require('./services/obsService');

async function healthCheck() {
  try {
    await obsService.createBucket();
    console.log('✅ OBS connection successful');
  } catch (error) {
    console.error('❌ OBS connection failed:', error);
  }
}
```

## Best Practices

1. **Security**
   - Use IAM roles instead of access keys when possible
   - Implement proper access controls
   - Enable encryption at rest

2. **Performance**
   - Implement client-side image compression
   - Use progressive JPEG format
   - Consider WebP format for modern browsers

3. **Monitoring**
   - Monitor upload success rates
   - Track storage usage
   - Set up alerts for failures

4. **Backup**
   - Implement cross-region replication
   - Regular backup verification
   - Document recovery procedures

## Support

For issues or questions:
1. Check the troubleshooting section
2. Review OBS service documentation
3. Contact the development team
