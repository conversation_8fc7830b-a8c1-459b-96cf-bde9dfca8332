import { createBrowserRouter, RouterProvider } from "react-router-dom";
import RootLayout from "./pages/RootLayout";
import ErrorPage from "./pages/ErrorPage";
import Login from "./pages/auth/Login";
import Signup from "./pages/auth/Signup";
import ForgotPassword from "./pages/auth/ForgotPassword";
import ResetPassword from "./pages/auth/ResetPassword";
import VerifyEmail from "./pages/auth/VerifyEmail";
import Profile from "./pages/auth/Profile";
import AuthDebug from "./pages/auth/AuthDebug";
import { PrivateRoutes } from "./routes/PrivateRoutes";
import { OpenRoutes } from "./routes/OpenRoutes";
import Product from "./pages/Product/Product";
// import { PRODUCTS } from "./pages/Product/Product";
// import Project from "./pages/Product/createProduct/Cloth/Project";
import Shop from "./pages/Shop.jsx";
import Favorites from "./pages/Favorites";
import AffiliateMarketing from "./pages/AffiliateMarketing/AffiliateMarketing";
import Dashboard from "./pages/AffiliateMarketing/Dashboard";
import ImageUpload from "./pages/AffiliateMarketing/ImageUpload";
import UserImages from "./pages/AffiliateMarketing/UserImages";
import Transactions from "./pages/AffiliateMarketing/Transactions";
import SavedDesigns from "./pages/SavedDesigns";
import Prod from "./pages/Product/createProduct/Cloth/Prod";
import Mug from "./pages/Product/createProduct/Mug";
// import Prod from "./pages/Product/createProduct/Cloth/Old-Prod";
import Coupons from "./pages/Coupons";
import Cart from "./pages/Cart/Cart";
import Checkout from "./pages/Checkout/Checkout";
// import Project from "./pages/CreateProject/Project/Project";
import ImageUploader from "./pages/ImageUploader";
import PreviewProduct from "./pages/Product/PreviewProduct";
import AffiliateProduct from "./pages/AffiliateMarketing/AffiliateProduct";
import LinkProduct from "./pages/Product/linkProduct/LinkProduct";
import OrderSuccess from "./pages/Checkout/OrderSuccess";
import OrderHistory from "./pages/OrderHistory/OrderHistory";
import Index from "./pages/Home/home1-jsx/Index";
import AboutUs from "./pages/pages/AboutUs.jsx";
import TermsAndConditions from "./pages/pages/TermsAndConditions.jsx";
import PrivacyPolicy from "./pages/pages/PrivacyPolicy.jsx";
import FAQ from "./pages/pages/FAQ.jsx";
import ContactUs from "./pages/pages/ContactUs.jsx";
import ShippingReturns from "./pages/pages/ShippingReturns.jsx";
import CookiePolicy from "./pages/pages/CookiePolicy.jsx";
import Maintenance from "./pages/Maintenance";
import AuthMigration from "./components/auth/AuthMigration";
import RateLimitExceededPage from "./pages/pages/RateLimitExceededPage.js";
import TelebirrTest from "./pages/TelebirrTest";

// Memoized router configuration to prevent recreation on every render
const router = createBrowserRouter([
  {
    path: "/maintenance",
    element: <Maintenance />,
  },
  {
    path: "/rate-limit-exceeded",
    element: <RateLimitExceededPage />,
  },
  {
    path: "/",
    element: (
      <>
        <RootLayout />
      </>
    ),
    errorElement: <ErrorPage />,
    children: [
      { index: true, element: <Index /> },
      {
        path: "/login",
        element: (
          <OpenRoutes>
            <Login />
          </OpenRoutes>
        ),
      },
      {
        path: "/signup",
        element: (
          <OpenRoutes>
            <Signup />
          </OpenRoutes>
        ),
      },
      { path: "/verify-email", element: <VerifyEmail /> },
      { path: "/forgot-password", element: <ForgotPassword /> },
      { path: "reset-password/:token", element: <ResetPassword /> },
      {
        path: "/auth-debug",
        element: (
          <PrivateRoutes>
            <AuthDebug />
          </PrivateRoutes>
        ),
      },
      { path: "/products", element: <Product /> },
      { path: "/shop", element: <Shop /> },
      {
        path: "/favorites",
        element: (
          <PrivateRoutes>
            <Favorites />
          </PrivateRoutes>
        ),
      },
      { path: "/image-upload", element: <ImageUpload /> },
      { path: "/products-details/:id", element: <Prod /> },
      { path: "/mug-editor/:id", element: <Mug /> },
      { path: "/preview-product/:id", element: <PreviewProduct /> },
      {
        path: "/affiliate",
        element: (
          <PrivateRoutes>
            <AffiliateMarketing />
          </PrivateRoutes>
        ),
        children: [
          { index: true, element: <Dashboard /> },
          { path: "image-upload", element: <ImageUpload /> },
          { path: "user-images", element: <UserImages /> },
          { path: "affiliate-products", element: <AffiliateProduct /> },
          { path: "transactions", element: <Transactions /> },
        ],
      },
      { path: "/product-link/:random", element: <LinkProduct /> },
      {
        path: "/saved-designs",
        element: (
          <PrivateRoutes>
            <SavedDesigns />
          </PrivateRoutes>
        ),
      },
      { path: "/coupons", element: <Coupons /> },
      {
        path: "/cart",
        element: (
          <PrivateRoutes>
            <Cart />
          </PrivateRoutes>
        ),
      },
      { path: "/checkout", element: <Checkout /> },
      { path: "/order-success", element: <OrderSuccess /> },
      {
        path: "/order-history",
        element: (
          <PrivateRoutes>
            <OrderHistory />
          </PrivateRoutes>
        ),
      },
      { path: "/image-uploader", element: <ImageUploader /> },
      { path: "/about-us", element: <AboutUs /> },
      { path: "/terms-and-conditions", element: <TermsAndConditions /> },
      { path: "/privacy-policy", element: <PrivacyPolicy /> },
      { path: "/faq", element: <FAQ /> },
      { path: "/contact-us", element: <ContactUs /> },
      { path: "/shipping-returns", element: <ShippingReturns /> },
      { path: "/cookie-policy", element: <CookiePolicy /> },
      { path: "/telebirr-test", element: <TelebirrTest /> },
      {
        path: "/profile",
        element: (
          <PrivateRoutes>
            <Profile />
          </PrivateRoutes>
        ),
      },
    ],
  },
]);

function App() {
  return <RouterProvider router={router} />;
}

export default App;
