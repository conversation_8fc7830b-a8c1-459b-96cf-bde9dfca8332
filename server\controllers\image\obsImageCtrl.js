const Image = require("../../models/image/imageModel");
const validateMongoDbId = require("../../utils/validateMongoDbId");
const asyncHandler = require("express-async-handler");
const formidable = require("formidable");
const obsService = require("../../services/obsService");
const imageCacheService = require("../../services/imageCacheService");

/**
 * OBS Image Controller
 * Handles all image operations using OBS (Object Storage Service)
 * This controller provides the same functionality as the original imageCtrl but uses OBS instead of Cloudinary
 */

const createOBSImage = asyncHandler(async (req, res) => {
  try {
    const { image, image_category, image_type } = req.body;

    // Validate MongoDB IDs
    validateMongoDbId(image_category);
    validateMongoDbId(image_type);

    const newImage = await Image.create({
      image,
      image_category,
      image_type,
    });

    // Invalidate image caches after creating new image
    await imageCacheService.invalidateImageCache(newImage._id, newImage);

    res.status(201).json({
      success: true,
      message: "Image created successfully with OBS",
      data: newImage
    });
  } catch (error) {
    console.error("Error in createOBSImage:", error);
    res.status(500).json({ 
      success: false,
      error: error.message 
    });
  }
});

const getAllOBSImages = asyncHandler(async (req, res) => {
  try {
    console.log("🔍 Fetching images directly from OBS bucket...");

    // Get images directly from OBS bucket
    const obsImages = await obsService.listImages({
      prefix: '', // Get all objects, not just images/ folder
      maxKeys: 1000
    });

    console.log(`📋 Found ${obsImages.length} objects in OBS bucket`);

    // Transform OBS objects to match expected format
    const transformedImages = obsImages.map(obsImage => ({
      _id: obsImage.key, // Use object key as ID
      objectKey: obsImage.key,
      image: [obsService.config.getObjectUrl(obsImage.key)], // Generate public URL
      size: obsImage.size,
      lastModified: obsImage.lastModified,
      contentType: obsImage.contentType || 'image/jpeg',
      source: 'obs',
      status: 'active' // Default status for OBS objects
    }));

    res.status(200).json({
      success: true,
      source: "obs-bucket",
      data: transformedImages,
      count: transformedImages.length,
      message: `Retrieved ${transformedImages.length} images from OBS bucket`
    });
  } catch (error) {
    console.error("❌ Error getting OBS bucket images:", error);
    res.status(500).json({
      success: false,
      error: error.message,
      message: "Failed to retrieve images from OBS bucket"
    });
  }
});

const getAllActiveOBSImages = asyncHandler(async (req, res) => {
  try {
    console.log("🔍 Fetching active images directly from OBS bucket...");

    // Get images directly from OBS bucket (all are considered active)
    const obsImages = await obsService.listImages({
      prefix: '', // Get all objects
      maxKeys: 1000
    });

    console.log(`📋 Found ${obsImages.length} active objects in OBS bucket`);

    // Transform OBS objects to match expected format
    const transformedImages = obsImages.map(obsImage => ({
      _id: obsImage.key,
      objectKey: obsImage.key,
      image: [obsService.config.getObjectUrl(obsImage.key)],
      size: obsImage.size,
      lastModified: obsImage.lastModified,
      contentType: obsImage.contentType || 'image/jpeg',
      source: 'obs',
      status: 'active'
    }));

    res.status(200).json({
      success: true,
      source: "obs-bucket",
      data: transformedImages,
      count: transformedImages.length,
      message: `Retrieved ${transformedImages.length} active images from OBS bucket`
    });
  } catch (error) {
    console.error("❌ Error getting active OBS bucket images:", error);
    res.status(500).json({
      success: false,
      error: error.message,
      message: "Failed to retrieve active images from OBS bucket"
    });
  }
});

/**
 * Get single OBS image by object key
 */
const getOBSImageById = asyncHandler(async (req, res) => {
  const { id } = req.params; // This will be the object key

  try {
    console.log(`🔍 Fetching OBS image with key: ${id}`);

    // Get image metadata from OBS
    const metadata = await obsService.getImageMetadata(id);

    // Transform to expected format
    const transformedImage = {
      _id: id,
      objectKey: id,
      image: [obsService.config.getObjectUrl(id)],
      size: metadata.size,
      lastModified: metadata.lastModified,
      contentType: metadata.contentType,
      source: 'obs',
      status: 'active',
      metadata: metadata.metadata
    };

    res.status(200).json({
      success: true,
      source: "obs-bucket",
      data: transformedImage,
      message: `Retrieved image ${id} from OBS bucket`
    });
  } catch (error) {
    console.error(`❌ Error getting OBS image ${id}:`, error);
    res.status(404).json({
      success: false,
      error: error.message,
      message: `Image ${id} not found in OBS bucket`
    });
  }
});

const updateOBSImage = asyncHandler(async (req, res) => {
  const { id } = req.params; // This is now the object key, not MongoDB ID
  
  try {
    // Log user role for debugging
    console.log("User role in updateOBSImage:", req.user.role);

    // Check if user is an administrator
    const isAdmin = req.user.role === "administrator";

    // If not admin, set status to pending
    const updatedData = isAdmin ? req.body : { ...req.body, status: "pending" };

    // If status is being changed to rejected, ensure rejectionReason is provided and set rejectedAt
    if (updatedData.status === "rejected") {
      if (!updatedData.rejectionReason) {
        return res.status(400).json({
          success: false,
          message: "Rejection reason is required when rejecting an image",
        });
      }
      updatedData.rejectedAt = new Date();
    }

    console.log("Is admin:", isAdmin);
    console.log("Updated data:", updatedData);

    const updatedImage = await Image.findByIdAndUpdate(id, updatedData, {
      new: true,
    });

    if (!updatedImage) {
      return res.status(404).json({
        success: false,
        message: "Image not found"
      });
    }

    // Invalidate image caches after update
    await imageCacheService.invalidateImageCache(id, updatedImage);

    res.status(200).json({
      success: true,
      message: "Image updated successfully",
      data: updatedImage
    });
  } catch (error) {
    console.error("Error in updateOBSImage:", error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

const deleteOBSImage = asyncHandler(async (req, res) => {
  const { id } = req.params; // This is now the object key

  try {
    console.log(`🗑️ Deleting OBS image with key: ${id}`);

    // Delete image directly from OBS bucket
    const result = await obsService.deleteImage(id);

    if (result) {
      console.log(`✅ Successfully deleted image from OBS: ${id}`);

      res.status(200).json({
        success: true,
        message: `Image ${id} deleted successfully from OBS bucket`,
        data: { objectKey: id }
      });
    } else {
      res.status(404).json({
        success: false,
        message: `Image ${id} not found in OBS bucket`
      });
    }
  } catch (error) {
    console.error(`❌ Error deleting OBS image ${id}:`, error);
    res.status(500).json({
      success: false,
      error: error.message,
      message: `Failed to delete image ${id} from OBS bucket`
        }
      }
    }

    // Now delete the image from the database
    const deletedImage = await Image.findByIdAndDelete(id);

    // Invalidate image caches after deletion
    await imageCacheService.invalidateImageCache(id, image);

    res.status(200).json({
      success: true,
      message: "Image deleted successfully",
      data: deletedImage
    });
  } catch (error) {
    console.error("Error in deleteOBSImage:", error);
    res.status(500).json({ 
      success: false,
      error: error.message 
    });
  }
});

const uploadOBSImage = asyncHandler(async (req, res) => {
  const form = new formidable.IncomingForm();
  form.multiples = true; // Allow multiple file uploads

  form.parse(req, async (err, fields, files) => {
    if (err) {
      return res.status(400).json({ 
        success: false,
        error: "File parsing error." 
      });
    }

    const images = Array.isArray(files.image) ? files.image : [files.image];

    if (!images.length || !images[0]) {
      return res.status(400).json({ 
        success: false,
        error: "No files uploaded." 
      });
    }

    try {
      const uploadedImages = await Promise.all(
        images.map(async (image) => {
          // Upload to OBS
          const result = await obsService.uploadImage(
            image.filepath,
            image.originalFilename || image.newFilename,
            {
              'x-obs-meta-uploader': req.user._id.toString(),
              'x-obs-meta-upload-source': 'onprintz-app'
            }
          );

          if (result.success) {
            // Validate category and type IDs
            const categories = Array.isArray(fields.image_categories) 
              ? fields.image_categories 
              : [fields.image_categories];
            const types = Array.isArray(fields.image_types) 
              ? fields.image_types 
              : [fields.image_types];

            categories.forEach((categoryId) => {
              validateMongoDbId(categoryId);
            });

            types.forEach((typeId) => {
              validateMongoDbId(typeId);
            });

            const newImageData = {
              image: [result.url],
              image_category: categories,
              image_type: types,
            };

            // Log user role for debugging
            console.log("User role in uploadOBSImage:", req.user.role);

            // Check if user is an administrator
            const isAdmin = req.user.role === "administrator";
            console.log("Is admin in uploadOBSImage:", isAdmin);

            // Only store uploader ID if the user is not an admin
            if (!isAdmin) {
              newImageData.uploader = req.user._id;
            }
            newImageData.status = "pending";

            return await Image.create(newImageData);
          } else {
            throw new Error("OBS image upload failed");
          }
        })
      );

      // Invalidate image caches after upload
      await imageCacheService.invalidateAllImageCaches();

      res.status(201).json({
        success: true,
        message: "Images uploaded successfully to OBS",
        data: uploadedImages
      });
    } catch (error) {
      console.error("Error in uploadOBSImage:", error);
      res.status(500).json({ 
        success: false,
        error: error.message 
      });
    }
  });
});

const updateOBSImageStatus = asyncHandler(async (req, res) => {
  const { id } = req.params;
  const { status } = req.body;

  validateMongoDbId(id);

  try {
    const updatedImage = await Image.findByIdAndUpdate(
      id,
      { status },
      { new: true }
    );

    if (!updatedImage) {
      return res.status(404).json({ 
        success: false,
        error: "Image not found" 
      });
    }

    // Invalidate image caches after status update
    await imageCacheService.invalidateImageCache(id, updatedImage);

    res.status(200).json({
      success: true,
      message: "Image status updated successfully",
      data: updatedImage
    });
  } catch (error) {
    console.error("Error in updateOBSImageStatus:", error);
    res.status(500).json({ 
      success: false,
      error: error.message 
    });
  }
});

// Bulk delete images (by filter or IDs)
const bulkDeleteOBSImages = asyncHandler(async (req, res) => {
  const { ids, status, startDate, endDate } = req.body;
  let filter = {};

  if (ids && Array.isArray(ids) && ids.length > 0) {
    filter._id = { $in: ids };
  } else {
    if (status) filter.status = status;
    if (startDate || endDate) {
      filter.createdAt = {};
      if (startDate) filter.createdAt.$gte = new Date(startDate);
      if (endDate) filter.createdAt.$lte = new Date(endDate);
    }
  }

  const images = await Image.find(filter);
  if (!images.length) {
    return res.status(404).json({ 
      success: false, 
      message: "No images found for deletion." 
    });
  }

  let successCount = 0;
  let failCount = 0;
  let errors = [];

  for (const image of images) {
    try {
      if (image.image && image.image.length > 0) {
        for (const imageUrl of image.image) {
          try {
            const objectKey = extractObjectKeyFromUrl(imageUrl);
            if (objectKey) {
              await obsService.deleteImage(objectKey);
            }
          } catch (obsErr) {
            errors.push({ id: image._id, imageUrl, error: obsErr.message });
          }
        }
      }
      await Image.findByIdAndDelete(image._id);
      successCount++;
    } catch (err) {
      failCount++;
      errors.push({ id: image._id, error: err.message });
    }
  }

  // Invalidate all image caches after bulk deletion
  await imageCacheService.invalidateAllImageCaches();

  res.json({
    success: true,
    message: "Bulk deletion completed",
    deleted: successCount,
    failed: failCount,
    errors,
  });
});

// Helper function to extract object key from OBS URL
const extractObjectKeyFromUrl = (url) => {
  try {
    const urlObj = new URL(url);
    // Remove leading slash and return the path
    return urlObj.pathname.substring(1);
  } catch (error) {
    console.error("Error extracting object key from URL:", error);
    return null;
  }
};

module.exports = {
  createOBSImage,
  getAllOBSImages,
  getAllActiveOBSImages,
  getOBSImageById,
  updateOBSImage,
  deleteOBSImage,
  uploadOBSImage,
  updateOBSImageStatus,
  bulkDeleteOBSImages,
};
