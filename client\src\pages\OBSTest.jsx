import React, { useState, useEffect } from 'react';
import { useSelector } from 'react-redux';
import OBSImageUpload from '../components/OBSImageUpload';
import OBSImageGallery from '../components/OBSImageGallery';
import toast from 'react-hot-toast';

/**
 * OBS Test Page
 * Test page for OBS integration functionality
 */
const OBSTest = () => {
  const [categories, setCategories] = useState([]);
  const [types, setTypes] = useState([]);
  const [loading, setLoading] = useState(true);
  const [selectedImages, setSelectedImages] = useState([]);
  const [refreshGallery, setRefreshGallery] = useState(0);

  const { user } = useSelector((state) => state.auth);

  useEffect(() => {
    loadCategoriesAndTypes();
  }, []);

  const loadCategoriesAndTypes = async () => {
    try {
      setLoading(true);
      
      // Load image categories
      const categoriesResponse = await fetch('/api/v1/image-category');
      const categoriesData = await categoriesResponse.json();
      
      // Load image types
      const typesResponse = await fetch('/api/v1/image-types');
      const typesData = await typesResponse.json();

      if (categoriesData.success) {
        setCategories(categoriesData.data || []);
      }
      
      if (typesData.success) {
        setTypes(typesData.data || []);
      }
    } catch (error) {
      console.error('Failed to load categories and types:', error);
      toast.error('Failed to load categories and types');
    } finally {
      setLoading(false);
    }
  };

  const handleUploadSuccess = (result) => {
    toast.success(`Successfully uploaded ${result.data?.length || 0} images to OBS`);
    setRefreshGallery(prev => prev + 1);
  };

  const handleUploadError = (error) => {
    toast.error(`Upload failed: ${error.message}`);
  };

  const handleImageSelect = (image) => {
    console.log('Selected image:', image);
    setSelectedImages(prev => {
      const isSelected = prev.some(img => img._id === image._id);
      if (isSelected) {
        return prev.filter(img => img._id !== image._id);
      } else {
        return [...prev, image];
      }
    });
  };

  const clearSelection = () => {
    setSelectedImages([]);
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading OBS test page...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            OBS Integration Test
          </h1>
          <p className="text-gray-600">
            Test the Object Storage Service (OBS) integration for image upload and management.
            <br />
            <strong>Note:</strong> This fetches images directly from your OBS bucket, not from the database.
          </p>
          
          {/* User Info */}
          {user && (
            <div className="mt-4 p-4 bg-blue-50 rounded-lg">
              <p className="text-sm text-blue-800">
                <strong>Logged in as:</strong> {user.firstname} {user.lastname} ({user.role})
              </p>
            </div>
          )}

          {/* Status Info */}
          <div className="mt-4 grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="bg-white p-4 rounded-lg shadow">
              <h3 className="font-medium text-gray-900">Categories Available</h3>
              <p className="text-2xl font-bold text-blue-600">{categories.length}</p>
            </div>
            <div className="bg-white p-4 rounded-lg shadow">
              <h3 className="font-medium text-gray-900">Types Available</h3>
              <p className="text-2xl font-bold text-green-600">{types.length}</p>
            </div>
            <div className="bg-white p-4 rounded-lg shadow">
              <h3 className="font-medium text-gray-900">Selected Images</h3>
              <p className="text-2xl font-bold text-purple-600">{selectedImages.length}</p>
              {selectedImages.length > 0 && (
                <button
                  onClick={clearSelection}
                  className="mt-2 text-sm text-red-600 hover:text-red-800"
                >
                  Clear Selection
                </button>
              )}
            </div>
          </div>
        </div>

        {/* Upload Section */}
        <div className="mb-8">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">
            Upload Images to OBS
          </h2>
          
          {categories.length === 0 || types.length === 0 ? (
            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
              <div className="flex">
                <svg className="h-5 w-5 text-yellow-400 mr-2" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                </svg>
                <div>
                  <h3 className="text-sm font-medium text-yellow-800">
                    Setup Required
                  </h3>
                  <p className="text-sm text-yellow-700 mt-1">
                    {categories.length === 0 && "No image categories found. "}
                    {types.length === 0 && "No image types found. "}
                    Please create categories and types before uploading images.
                  </p>
                </div>
              </div>
            </div>
          ) : (
            <OBSImageUpload
              categories={categories}
              types={types}
              onUploadSuccess={handleUploadSuccess}
              onUploadError={handleUploadError}
              multiple={true}
              className="mb-6"
            />
          )}
        </div>

        {/* Gallery Section */}
        <div className="mb-8">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">
            OBS Image Gallery
          </h2>
          
          <OBSImageGallery
            key={refreshGallery} // Force refresh when upload succeeds
            showActions={true}
            onImageSelect={handleImageSelect}
            selectedImages={selectedImages.map(img => img._id)}
            userRole={user?.role || 'user'}
            className="bg-white rounded-lg shadow p-6"
          />
        </div>

        {/* Selected Images Info */}
        {selectedImages.length > 0 && (
          <div className="mb-8">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">
              Selected Images ({selectedImages.length})
            </h2>
            
            <div className="bg-white rounded-lg shadow p-6">
              <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
                {selectedImages.map((image) => (
                  <div key={image._id} className="relative">
                    <img
                      src={image.image[0]}
                      alt={`Selected ${image._id}`}
                      className="w-full h-24 object-cover rounded border-2 border-blue-500"
                    />
                    <div className="absolute top-1 right-1 bg-blue-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs">
                      ✓
                    </div>
                  </div>
                ))}
              </div>
              
              <div className="mt-4 flex space-x-2">
                <button
                  onClick={clearSelection}
                  className="px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700"
                >
                  Clear Selection
                </button>
                <button
                  onClick={() => {
                    const urls = selectedImages.map(img => img.image[0]).join('\n');
                    navigator.clipboard.writeText(urls);
                    toast.success('Image URLs copied to clipboard');
                  }}
                  className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
                >
                  Copy URLs
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Debug Info */}
        <div className="mb-8">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">
            Debug Information
          </h2>
          
          <div className="bg-white rounded-lg shadow p-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h3 className="font-medium text-gray-900 mb-2">Available Categories</h3>
                <div className="max-h-40 overflow-y-auto">
                  {categories.length > 0 ? (
                    <ul className="text-sm text-gray-600 space-y-1">
                      {categories.map((category) => (
                        <li key={category._id} className="flex justify-between">
                          <span>{category.name}</span>
                          <span className="text-gray-400">{category._id}</span>
                        </li>
                      ))}
                    </ul>
                  ) : (
                    <p className="text-sm text-gray-500">No categories available</p>
                  )}
                </div>
              </div>
              
              <div>
                <h3 className="font-medium text-gray-900 mb-2">Available Types</h3>
                <div className="max-h-40 overflow-y-auto">
                  {types.length > 0 ? (
                    <ul className="text-sm text-gray-600 space-y-1">
                      {types.map((type) => (
                        <li key={type._id} className="flex justify-between">
                          <span>{type.name}</span>
                          <span className="text-gray-400">{type._id}</span>
                        </li>
                      ))}
                    </ul>
                  ) : (
                    <p className="text-sm text-gray-500">No types available</p>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Instructions */}
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
          <h3 className="font-medium text-blue-900 mb-2">
            How to Test OBS Integration
          </h3>
          <ol className="text-sm text-blue-800 space-y-1 list-decimal list-inside">
            <li>Ensure you have image categories and types created in the admin panel</li>
            <li>Select one or more image files using the upload component above</li>
            <li>Choose appropriate categories and types for your images</li>
            <li>Click "Upload to OBS" to test the upload functionality</li>
            <li>Check the gallery below to see uploaded images</li>
            <li>Test image selection and management features</li>
            <li>Verify that images are stored in OBS and accessible via the generated URLs</li>
          </ol>
        </div>
      </div>
    </div>
  );
};

export default OBSTest;
