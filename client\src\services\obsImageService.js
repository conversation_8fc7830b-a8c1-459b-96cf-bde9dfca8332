import axios from 'axios';

/**
 * OBS Image Service
 * Handles all API calls for OBS-based image operations
 */

const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:5000/api';
const OBS_IMAGE_ENDPOINT = `${API_BASE_URL}/obs-images`;

// Create axios instance with default config
const obsImageAPI = axios.create({
  baseURL: OBS_IMAGE_ENDPOINT,
  timeout: 30000, // 30 seconds timeout for uploads
});

// Add auth token to requests
obsImageAPI.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token') || sessionStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor for error handling
obsImageAPI.interceptors.response.use(
  (response) => response,
  (error) => {
    console.error('OBS Image API Error:', error.response?.data || error.message);
    return Promise.reject(error);
  }
);

/**
 * Upload images to OBS
 * @param {FileList|File[]} files - Files to upload
 * @param {string[]} categories - Image category IDs
 * @param {string[]} types - Image type IDs
 * @param {Function} onProgress - Progress callback
 * @returns {Promise<Object>} Upload result
 */
export const uploadImagesToOBS = async (files, categories, types, onProgress) => {
  try {
    const formData = new FormData();
    
    // Add files
    Array.from(files).forEach((file, index) => {
      formData.append('image', file);
    });
    
    // Add categories and types
    categories.forEach(categoryId => {
      formData.append('image_categories', categoryId);
    });
    
    types.forEach(typeId => {
      formData.append('image_types', typeId);
    });

    const response = await obsImageAPI.post('/upload', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
      onUploadProgress: (progressEvent) => {
        if (onProgress) {
          const percentCompleted = Math.round(
            (progressEvent.loaded * 100) / progressEvent.total
          );
          onProgress(percentCompleted);
        }
      },
    });

    return response.data;
  } catch (error) {
    throw new Error(error.response?.data?.error || 'Failed to upload images to OBS');
  }
};

/**
 * Get all images from OBS
 * @returns {Promise<Object>} Images data
 */
export const getAllOBSImages = async () => {
  try {
    const response = await obsImageAPI.get('/');
    return response.data;
  } catch (error) {
    throw new Error(error.response?.data?.error || 'Failed to fetch OBS images');
  }
};

/**
 * Get all active images from OBS
 * @returns {Promise<Object>} Active images data
 */
export const getAllActiveOBSImages = async () => {
  try {
    const response = await obsImageAPI.get('/active');
    return response.data;
  } catch (error) {
    throw new Error(error.response?.data?.error || 'Failed to fetch active OBS images');
  }
};

/**
 * Create a new image record
 * @param {Object} imageData - Image data
 * @returns {Promise<Object>} Created image
 */
export const createOBSImage = async (imageData) => {
  try {
    const response = await obsImageAPI.post('/', imageData);
    return response.data;
  } catch (error) {
    throw new Error(error.response?.data?.error || 'Failed to create OBS image');
  }
};

/**
 * Update an image
 * @param {string} imageId - Image ID
 * @param {Object} updateData - Update data
 * @returns {Promise<Object>} Updated image
 */
export const updateOBSImage = async (imageId, updateData) => {
  try {
    const response = await obsImageAPI.put(`/${imageId}`, updateData);
    return response.data;
  } catch (error) {
    throw new Error(error.response?.data?.error || 'Failed to update OBS image');
  }
};

/**
 * Delete an image
 * @param {string} imageId - Image ID
 * @returns {Promise<Object>} Deletion result
 */
export const deleteOBSImage = async (imageId) => {
  try {
    const response = await obsImageAPI.delete(`/${imageId}`);
    return response.data;
  } catch (error) {
    throw new Error(error.response?.data?.error || 'Failed to delete OBS image');
  }
};

/**
 * Update image status
 * @param {string} imageId - Image ID
 * @param {string} status - New status
 * @returns {Promise<Object>} Updated image
 */
export const updateOBSImageStatus = async (imageId, status) => {
  try {
    const response = await obsImageAPI.patch(`/${imageId}/status`, { status });
    return response.data;
  } catch (error) {
    throw new Error(error.response?.data?.error || 'Failed to update OBS image status');
  }
};

/**
 * Bulk delete images
 * @param {Object} deleteParams - Delete parameters
 * @returns {Promise<Object>} Deletion result
 */
export const bulkDeleteOBSImages = async (deleteParams) => {
  try {
    const response = await obsImageAPI.delete('/bulk', { data: deleteParams });
    return response.data;
  } catch (error) {
    throw new Error(error.response?.data?.error || 'Failed to bulk delete OBS images');
  }
};

/**
 * Get image upload progress
 * @param {string} uploadId - Upload ID
 * @returns {Promise<Object>} Upload progress
 */
export const getOBSUploadProgress = async (uploadId) => {
  try {
    const response = await obsImageAPI.get(`/upload-progress/${uploadId}`);
    return response.data;
  } catch (error) {
    throw new Error(error.response?.data?.error || 'Failed to get upload progress');
  }
};

/**
 * Cancel image upload
 * @param {string} uploadId - Upload ID
 * @returns {Promise<Object>} Cancellation result
 */
export const cancelOBSUpload = async (uploadId) => {
  try {
    const response = await obsImageAPI.post(`/cancel-upload/${uploadId}`);
    return response.data;
  } catch (error) {
    throw new Error(error.response?.data?.error || 'Failed to cancel upload');
  }
};

/**
 * Validate image before upload
 * @param {File} file - File to validate
 * @returns {Object} Validation result
 */
export const validateImageFile = (file) => {
  const maxSize = 10 * 1024 * 1024; // 10MB
  const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
  
  const errors = [];
  
  if (!allowedTypes.includes(file.type)) {
    errors.push('File type not supported. Please use JPEG, PNG, GIF, or WebP.');
  }
  
  if (file.size > maxSize) {
    errors.push('File size too large. Maximum size is 10MB.');
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
};

/**
 * Generate thumbnail URL for OBS image
 * @param {string} imageUrl - Original image URL
 * @param {Object} options - Thumbnail options
 * @returns {string} Thumbnail URL
 */
export const generateOBSThumbnail = (imageUrl, options = {}) => {
  const { width = 300, height = 300, quality = 80 } = options;
  
  // For OBS, we might need to implement thumbnail generation on the server
  // or use a separate service. For now, return the original URL
  // In production, you might want to implement server-side thumbnail generation
  return imageUrl;
};

/**
 * Check if image URL is from OBS
 * @param {string} url - Image URL
 * @returns {boolean} Is OBS URL
 */
export const isOBSImageUrl = (url) => {
  if (!url) return false;
  
  // Check if URL contains OBS endpoint
  const obsEndpoint = process.env.REACT_APP_OBS_ENDPOINT;
  return obsEndpoint ? url.includes(obsEndpoint) : false;
};

/**
 * Get image metadata from OBS
 * @param {string} imageUrl - Image URL
 * @returns {Promise<Object>} Image metadata
 */
export const getOBSImageMetadata = async (imageUrl) => {
  try {
    // Extract object key from URL
    const objectKey = imageUrl.split('/').pop();
    const response = await obsImageAPI.get(`/metadata/${objectKey}`);
    return response.data;
  } catch (error) {
    throw new Error(error.response?.data?.error || 'Failed to get image metadata');
  }
};

export default {
  uploadImagesToOBS,
  getAllOBSImages,
  getAllActiveOBSImages,
  createOBSImage,
  updateOBSImage,
  deleteOBSImage,
  updateOBSImageStatus,
  bulkDeleteOBSImages,
  getOBSUploadProgress,
  cancelOBSUpload,
  validateImageFile,
  generateOBSThumbnail,
  isOBSImageUrl,
  getOBSImageMetadata
};
