const express = require("express");
const router = express.Router();
const {
  createOBSImage,
  getAllOBSImages,
  getAllActiveOBSImages,
  updateOBSImage,
  deleteOBSImage,
  uploadOBSImage,
  updateOBSImageStatus,
  bulkDeleteOBSImages,
} = require("../../controllers/image/obsImageCtrl");

const { authMiddleware, isAdmin } = require("../../middlewares/authMiddleware");
const { validateUser } = require("../../middlewares/validateUser");

/**
 * OBS Image Routes
 * All routes for OBS-based image operations
 * These routes mirror the original image routes but use OBS storage
 */

// Public routes (no authentication required)
router.get("/active", getAllActiveOBSImages); // Get all active images

// Protected routes (authentication required)
router.use(authMiddleware); // Apply authentication to all routes below

// Image CRUD operations
router.post("/", validateUser, createOBSImage); // Create new image record
router.get("/", getAllOBSImages); // Get all images (admin/user based on role)
router.put("/:id", validateUser, updateOBSImage); // Update image
router.delete("/:id", validateUser, deleteOBSImage); // Delete image

// Image upload operations
router.post("/upload", validateUser, uploadOBSImage); // Upload image to OBS

// Image status management
router.patch("/:id/status", validateUser, updateOBSImageStatus); // Update image status

// Admin-only routes
router.delete("/bulk", isAdmin, bulkDeleteOBSImages); // Bulk delete images

module.exports = router;
