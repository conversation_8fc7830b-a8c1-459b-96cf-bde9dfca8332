import React, { useState, useEffect } from 'react';
import { 
  getAllActiveOBSImages, 
  deleteOBSImage, 
  updateOBSImageStatus,
  isOBSImageUrl 
} from '../services/obsImageService';
import toast from 'react-hot-toast';

/**
 * OBS Image Gallery Component
 * Displays images stored in OBS with management capabilities
 */
const OBSImageGallery = ({ 
  showActions = true, 
  onImageSelect, 
  selectedImages = [],
  className = "",
  userRole = "user"
}) => {
  const [images, setImages] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [selectedImageIds, setSelectedImageIds] = useState(selectedImages);

  // Load images on component mount
  useEffect(() => {
    loadImages();
  }, []);

  // Update selected images when prop changes
  useEffect(() => {
    setSelectedImageIds(selectedImages);
  }, [selectedImages]);

  const loadImages = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await getAllActiveOBSImages();
      setImages(response.data || []);
      
      toast.success(`Loaded ${response.data?.length || 0} images from OBS`);
    } catch (err) {
      console.error('Failed to load OBS images:', err);
      setError(err.message);
      toast.error('Failed to load images from OBS');
    } finally {
      setLoading(false);
    }
  };

  const handleImageSelect = (image) => {
    if (onImageSelect) {
      onImageSelect(image);
    }
    
    // Toggle selection
    const isSelected = selectedImageIds.includes(image._id);
    if (isSelected) {
      setSelectedImageIds(prev => prev.filter(id => id !== image._id));
    } else {
      setSelectedImageIds(prev => [...prev, image._id]);
    }
  };

  const handleDeleteImage = async (imageId) => {
    if (!window.confirm('Are you sure you want to delete this image from OBS?')) {
      return;
    }

    try {
      await deleteOBSImage(imageId);
      setImages(prev => prev.filter(img => img._id !== imageId));
      setSelectedImageIds(prev => prev.filter(id => id !== imageId));
      toast.success('Image deleted from OBS successfully');
    } catch (err) {
      console.error('Failed to delete image:', err);
      toast.error('Failed to delete image from OBS');
    }
  };

  const handleStatusChange = async (imageId, newStatus) => {
    try {
      await updateOBSImageStatus(imageId, newStatus);
      setImages(prev => 
        prev.map(img => 
          img._id === imageId ? { ...img, status: newStatus } : img
        )
      );
      toast.success(`Image status updated to ${newStatus}`);
    } catch (err) {
      console.error('Failed to update image status:', err);
      toast.error('Failed to update image status');
    }
  };

  const getImageUrl = (image) => {
    if (image.image && image.image.length > 0) {
      return image.image[0];
    }
    return null;
  };

  const isImageSelected = (imageId) => {
    return selectedImageIds.includes(imageId);
  };

  if (loading) {
    return (
      <div className={`obs-image-gallery ${className}`}>
        <div className="loading-container flex items-center justify-center p-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          <span className="ml-2 text-gray-600">Loading OBS images...</span>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={`obs-image-gallery ${className}`}>
        <div className="error-container bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex items-center">
            <svg className="h-5 w-5 text-red-400 mr-2" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
            </svg>
            <span className="text-red-800">Error loading OBS images: {error}</span>
          </div>
          <button
            onClick={loadImages}
            className="mt-2 px-3 py-1 bg-red-600 text-white rounded text-sm hover:bg-red-700"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className={`obs-image-gallery ${className}`}>
      <div className="gallery-header flex justify-between items-center mb-4">
        <h3 className="text-lg font-semibold text-gray-800">
          OBS Image Gallery ({images.length} images)
        </h3>
        <div className="gallery-actions space-x-2">
          <button
            onClick={loadImages}
            className="px-3 py-1 bg-blue-600 text-white rounded text-sm hover:bg-blue-700"
          >
            Refresh
          </button>
          {selectedImageIds.length > 0 && (
            <span className="text-sm text-gray-600">
              {selectedImageIds.length} selected
            </span>
          )}
        </div>
      </div>

      {images.length === 0 ? (
        <div className="empty-state text-center py-8">
          <svg className="mx-auto h-12 w-12 text-gray-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
          </svg>
          <p className="text-gray-600">No images found in OBS storage</p>
        </div>
      ) : (
        <div className="image-grid grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-6 gap-4">
          {images.map((image) => {
            const imageUrl = getImageUrl(image);
            const isSelected = isImageSelected(image._id);
            const isOBSImage = isOBSImageUrl(imageUrl);

            return (
              <div
                key={image._id}
                className={`image-card relative group cursor-pointer rounded-lg overflow-hidden border-2 transition-all ${
                  isSelected 
                    ? 'border-blue-500 shadow-lg' 
                    : 'border-gray-200 hover:border-gray-300'
                }`}
                onClick={() => handleImageSelect(image)}
              >
                {/* Image */}
                <div className="aspect-square bg-gray-100">
                  {imageUrl ? (
                    <img
                      src={imageUrl}
                      alt={`Image ${image._id}`}
                      className="w-full h-full object-cover"
                      loading="lazy"
                      onError={(e) => {
                        e.target.src = '/placeholder-image.png';
                      }}
                    />
                  ) : (
                    <div className="w-full h-full flex items-center justify-center">
                      <svg className="h-8 w-8 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clipRule="evenodd" />
                      </svg>
                    </div>
                  )}
                </div>

                {/* OBS Badge */}
                {isOBSImage && (
                  <div className="absolute top-2 left-2">
                    <span className="bg-green-500 text-white text-xs px-2 py-1 rounded">
                      OBS
                    </span>
                  </div>
                )}

                {/* Selection Indicator */}
                {isSelected && (
                  <div className="absolute top-2 right-2">
                    <div className="bg-blue-500 text-white rounded-full p-1">
                      <svg className="h-4 w-4" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                      </svg>
                    </div>
                  </div>
                )}

                {/* Status Badge */}
                <div className="absolute bottom-2 left-2">
                  <span className={`text-xs px-2 py-1 rounded ${
                    image.status === 'active' ? 'bg-green-100 text-green-800' :
                    image.status === 'pending' ? 'bg-yellow-100 text-yellow-800' :
                    image.status === 'rejected' ? 'bg-red-100 text-red-800' :
                    'bg-gray-100 text-gray-800'
                  }`}>
                    {image.status}
                  </span>
                </div>

                {/* Actions Overlay */}
                {showActions && (
                  <div className="absolute inset-0 bg-black bg-opacity-50 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center space-x-2">
                    {userRole === 'administrator' && (
                      <>
                        <select
                          value={image.status}
                          onChange={(e) => {
                            e.stopPropagation();
                            handleStatusChange(image._id, e.target.value);
                          }}
                          className="text-xs px-2 py-1 rounded"
                          onClick={(e) => e.stopPropagation()}
                        >
                          <option value="active">Active</option>
                          <option value="pending">Pending</option>
                          <option value="rejected">Rejected</option>
                          <option value="inactive">Inactive</option>
                        </select>
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            handleDeleteImage(image._id);
                          }}
                          className="bg-red-600 text-white p-1 rounded hover:bg-red-700"
                          title="Delete Image"
                        >
                          <svg className="h-4 w-4" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M9 2a1 1 0 000 2h2a1 1 0 100-2H9zM4 5a2 2 0 012-2h8a2 2 0 012 2v6a2 2 0 01-2 2H6a2 2 0 01-2-2V5zM8 8a1 1 0 012 0v3a1 1 0 11-2 0V8zm4 0a1 1 0 012 0v3a1 1 0 11-2 0V8z" clipRule="evenodd" />
                          </svg>
                        </button>
                      </>
                    )}
                  </div>
                )}
              </div>
            );
          })}
        </div>
      )}
    </div>
  );
};

export default OBSImageGallery;
